'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import { Settings, ExternalLink, LogOut } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface DashboardHeaderProps {
  user: {
    name?: string | null
    email?: string | null
    image?: string | null
  }
}

export function DashboardHeader({ user }: DashboardHeaderProps) {
  const router = useRouter()
  const initials = user.name
    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : user.email?.[0]?.toUpperCase() || 'U'
console.log({ user })
  return (
    <header className="bg-white border-b border-gray-200 lg:pl-64 sticky top-0 z-40">
      <div className="flex items-center justify-between h-16 px-4 sm:px-6">
        {/* Left side - could add breadcrumbs or page title here */}
        <div className="flex-1 lg:pl-4" />

        {/* Right side - user menu */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* View Profile Link */}
          <Link href={`/${user.email?.split('@')[0] || 'profile'}`} target="_blank">
            <Button 
              variant="outline" 
              size="sm"
              className="hidden sm:inline-flex"
              aria-label="View your public profile in new tab"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View Profile
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              className="sm:hidden"
              aria-label="View your public profile in new tab"
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </Link>

          {/* User dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="relative h-8 w-8 rounded-full"
                aria-label={`User menu for ${user.name || user.email}`}
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.image || undefined} alt={`${user.name || 'User'} profile picture`} />
                  <AvatarFallback aria-label={`${user.name || 'User'} initials`}>
                    {initials}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user.name || 'User'}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => router.push('/dashboard/settings')}
                className="cursor-pointer"
              >
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => {
                  // Import signOut dynamically to avoid SSR issues
                  import('next-auth/react').then(({ signOut }) => {
                    signOut({ callbackUrl: '/' })
                  })
                }}
                className="cursor-pointer"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}